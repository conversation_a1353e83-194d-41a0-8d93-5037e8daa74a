# ================================
# 简化构建 - 直接使用已构建的dist目录
# ================================

# ================================
# 生产环境 - Nginx阶段
# ================================
FROM nginx:1.25-alpine

# 安装必要工具
RUN apk add --no-cache curl

# 创建nginx用户和组（安全性）
RUN addgroup -g 1001 -S nginx-app && \
    adduser -S nginx-app -u 1001

# 复制已构建的dist目录到Nginx服务目录
COPY dist /usr/share/nginx/html

# 复制优化的Nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 设置正确的文件权限
RUN chown -R nginx-app:nginx-app /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# 创建nginx运行所需的目录
RUN mkdir -p /var/cache/nginx/client_temp && \
    mkdir -p /var/cache/nginx/proxy_temp && \
    mkdir -p /var/cache/nginx/fastcgi_temp && \
    mkdir -p /var/cache/nginx/uwsgi_temp && \
    mkdir -p /var/cache/nginx/scgi_temp && \
    chown -R nginx-app:nginx-app /var/cache/nginx

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# 启动Nginx（使用默认nginx用户）
CMD ["nginx", "-g", "daemon off;"]
