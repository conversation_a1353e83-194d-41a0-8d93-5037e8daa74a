# ================================
# Docker构建忽略文件
# 用于排除不必要的文件和目录
# ================================

# Node.js相关（重要：排除node_modules避免冲突）
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity
package-lock.json

# 构建产物（避免冲突）
# dist/ - 注释掉，因为Docker构建需要使用已构建的dist目录
build/
coverage/

# 开发工具
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Git相关
.git/
.gitignore
.gitattributes

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log

# 临时文件
tmp/
temp/
.tmp/

# 测试相关
.nyc_output/
coverage/
.coverage
.pytest_cache/
.tox/

# 文档和说明
README.md
*.md
docs/

# 配置文件（保留构建必需的文件）
.eslintrc*
.prettierrc*
jest.config.*
# tsconfig.json - 构建需要
# tsconfig.node.json - 构建需要
# vite.config.* - 构建需要

# 脚本文件
*.bat
*.sh
scripts/

# 测试文件
tests/
test/
__tests__/
*.test.*
*.spec.*

# 其他开发文件
.editorconfig
.browserslistrc
.nvmrc
.node-version

# Windows特定
*.exe
*.msi
*.lnk

# 备份文件
*.bak
*.backup
*.old

# 修复脚本和临时文件
fix-*.js
batch-*.js
create-*.js
check-*.js
quick-*.js
final-*.js
test-*.js
test-*.cjs
*.cjs

# 错误修复相关文档
ERROR_FIXES_SUMMARY.md
INTEGRATION_TEST_SUMMARY.md
TEST_SUMMARY.md
TabPane修复指南.md
Windows部署说明.md
